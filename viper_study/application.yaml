apiVersion: v1
kind: KafkaCluster2
metadata:
  name: kafka-operator
  labels:
    config1:
      address: kafka-operator-labels-01
      id: kafka-operator-labels-02
      name: mysql-example-cluster-master
      nodeName: ************
      role: master
    config2:
      address: kafka-operator-labels-01
      id: kafka-operator-labels-02
      name: mysql-example-cluster-slave
      nodeName: *************
      role: slave
spec:
  replicas: 1
  name: kafka-controller
  image: ***********:5000/nginx
  ports: 8088
  conditions:
    - containerPort: 8080
      requests:
        cpu: "0.25"
        memory: "512Mi"
      limits:
        cpu: "0.25"
        memory: "1Gi"
    - containerPort: 9090
      requests:
        cpu: "0.33"
        memory: "333Mi"
      limits:
        cpu: "0.55"
        memory: "5Gi"